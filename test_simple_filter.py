#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化过滤配置
只通过交易量、流动性和价格过滤
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_filter():
    """测试简化的过滤配置"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print("🔍 测试简化过滤配置")
        print("=" * 50)
        print("只通过交易量、流动性和价格进行过滤")
        print("=" * 50)
        
        # 创建分析器实例
        analyzer = PythiaIntegratedAnalyzer()
        
        # 模拟真实的PYTHIA交易对数据（包含各种情况）
        test_pairs = [
            # 正常交易对1
            {
                'pairAddress': 'normal_pair_1',
                'marketCap': 50000,           # 5万市值
                'liquidity': {'usd': 5000},   # 5千流动性
                'volume': {'h24': 1000},      # 1千交易量
                'priceUsd': '0.001',
                'priceChange': {'h24': 15.0},
                'txns': {'h24': {'buys': 10, 'sells': 8}},
                'dexId': 'unknown_dex',
                'baseToken': {'symbol': 'PYTHIA'},
                'quoteToken': {'symbol': 'USDT'},
                'pairCreatedAt': time.time() - 3600  # 1小时前
            },
            # 正常交易对2
            {
                'pairAddress': 'normal_pair_2',
                'marketCap': 20000,           # 2万市值
                'liquidity': {'usd': 2000},   # 2千流动性
                'volume': {'h24': 500},       # 500交易量
                'priceUsd': '0.0001',
                'priceChange': {'h24': -8.5},
                'txns': {'h24': {'buys': 5, 'sells': 7}},
                'dexId': 'small_dex',
                'baseToken': {'symbol': 'PYTHIA'},
                'quoteToken': {'symbol': 'ETH'},
                'pairCreatedAt': time.time() - 1800  # 30分钟前
            },
            # 极低流动性交易对 - 应该被过滤
            {
                'pairAddress': 'low_liquidity',
                'marketCap': 1000,
                'liquidity': {'usd': 5},      # 5美元流动性 (低于10美元阈值)
                'volume': {'h24': 100},
                'priceUsd': '0.01',
                'priceChange': {'h24': 50.0},
                'txns': {'h24': {'buys': 2, 'sells': 1}},
                'dexId': 'tiny_dex',
                'baseToken': {'symbol': 'PYTHIA'},
                'quoteToken': {'symbol': 'USDC'},
                'pairCreatedAt': time.time() - 600  # 10分钟前
            },
            # 极低交易量交易对 - 应该被过滤
            {
                'pairAddress': 'low_volume',
                'marketCap': 5000,
                'liquidity': {'usd': 100},
                'volume': {'h24': 5},         # 5美元交易量 (低于10美元阈值)
                'priceUsd': '0.005',
                'priceChange': {'h24': 25.0},
                'txns': {'h24': {'buys': 1, 'sells': 1}},
                'dexId': 'inactive_dex',
                'baseToken': {'symbol': 'PYTHIA'},
                'quoteToken': {'symbol': 'BNB'},
                'pairCreatedAt': time.time() - 7200  # 2小时前
            },
            # 边界情况 - 刚好达到阈值
            {
                'pairAddress': 'boundary_case',
                'marketCap': 1,               # 刚好达到1美元市值阈值
                'liquidity': {'usd': 10},     # 刚好达到10美元流动性阈值
                'volume': {'h24': 10},        # 刚好达到10美元交易量阈值
                'priceUsd': '0.000000001',    # 极低价格
                'priceChange': {'h24': 1000.0}, # 极高波动
                'txns': {'h24': {'buys': 0, 'sells': 0}}, # 无交易
                'dexId': 'edge_dex',
                'baseToken': {},              # 空代币信息
                'quoteToken': {},
                'pairCreatedAt': time.time()  # 刚创建
            }
        ]
        
        print(f"📊 测试数据:")
        print(f"   总交易对数: {len(test_pairs)}")
        print(f"   预期通过: 3个 (2个正常 + 1个边界)")
        print(f"   预期过滤: 2个 (低流动性 + 低交易量)")
        
        # 执行过滤
        print(f"\n🔍 执行简化过滤...")
        start_time = time.time()
        
        filtered_pairs, filter_stats = analyzer.filter_pairs_by_criteria(test_pairs)
        
        filter_time = time.time() - start_time
        
        print(f"\n📈 过滤结果:")
        print(f"   过滤耗时: {filter_time:.3f}秒")
        print(f"   原始交易对: {filter_stats['total_pairs']}")
        print(f"   过滤后交易对: {filter_stats['final_pairs']}")
        print(f"   过滤率: {((filter_stats['total_pairs'] - filter_stats['final_pairs']) / filter_stats['total_pairs'] * 100):.1f}%")
        
        # 显示详细过滤统计
        print(f"\n📋 详细过滤统计:")
        print(f"   市值过滤: {filter_stats.get('filtered_by_market_cap', 0)}")
        print(f"   流动性过滤: {filter_stats.get('filtered_by_liquidity', 0)}")
        print(f"   交易量过滤: {filter_stats.get('filtered_by_volume', 0)}")
        print(f"   价格过滤: {filter_stats.get('filtered_by_price', 0)}")
        print(f"   其他过滤: {filter_stats.get('filtered_by_transactions', 0) + filter_stats.get('filtered_by_quality', 0)}")
        
        # 显示通过过滤的交易对
        print(f"\n✅ 通过过滤的交易对:")
        for i, pair in enumerate(filtered_pairs, 1):
            print(f"   {i}. {pair['pairAddress']}")
            print(f"      市值: ${pair['marketCap']:,}")
            print(f"      流动性: ${pair['liquidity']['usd']:,}")
            print(f"      交易量: ${pair['volume']['h24']:,}")
            print(f"      价格: ${pair['priceUsd']}")
            print(f"      DEX: {pair['dexId']}")
        
        # 验证过滤效果
        print(f"\n🎯 过滤效果验证:")
        expected_count = 3  # 预期通过的交易对数量
        if filter_stats['final_pairs'] >= expected_count:
            print(f"   ✅ 过滤效果良好! 通过了{filter_stats['final_pairs']}个交易对")
            print(f"   ✅ 简化过滤成功，保留了足够的数据进行分析")
        else:
            print(f"   ⚠️ 过滤可能仍然过于严格，只通过了{filter_stats['final_pairs']}个交易对")
            
        # 性能分析
        if filter_stats['final_pairs'] > 0:
            print(f"\n🚀 性能分析:")
            reduction = (filter_stats['total_pairs'] - filter_stats['final_pairs']) / filter_stats['total_pairs'] * 100
            print(f"   数据减少量: {reduction:.0f}%")
            if reduction > 0:
                print(f"   预期性能提升: {reduction * 0.5:.0f}%")  # 简化过滤的性能提升较小
            print(f"   过滤开销: 几乎为零")
            print(f"   适用场景: 保留最大数据量，最小过滤开销")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_pythia_search():
    """测试真实的PYTHIA搜索和过滤"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print(f"\n🔍 测试真实PYTHIA数据搜索和过滤")
        print("=" * 50)
        
        analyzer = PythiaIntegratedAnalyzer()
        
        print("🔍 搜索PYTHIA交易对...")
        start_time = time.time()
        
        pairs_data, filter_stats = analyzer.search_all_pythia_pairs()
        
        search_time = time.time() - start_time
        
        print(f"\n📊 搜索结果:")
        print(f"   搜索耗时: {search_time:.2f}秒")
        print(f"   找到交易对: {len(pairs_data)}个")
        
        if len(pairs_data) > 0:
            print(f"   ✅ 成功! 简化过滤配置有效")
            print(f"   📈 过滤统计: {filter_stats}")
            
            # 显示前几个交易对的信息
            print(f"\n📋 前3个交易对示例:")
            for i, pair in enumerate(pairs_data[:3], 1):
                print(f"   {i}. 地址: {pair.get('pairAddress', 'N/A')[:20]}...")
                print(f"      市值: ${pair.get('marketCap', 0):,}")
                print(f"      流动性: ${pair.get('liquidity', {}).get('usd', 0):,}")
                print(f"      交易量: ${pair.get('volume', {}).get('h24', 0):,}")
                print(f"      DEX: {pair.get('dexId', 'N/A')}")
        else:
            print(f"   ⚠️ 仍然没有找到交易对，可能需要进一步放宽过滤条件")
            
        return len(pairs_data) > 0
        
    except Exception as e:
        print(f"❌ 真实搜索测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 PYTHIA简化过滤配置测试")
    print("=" * 60)
    
    # 测试1: 模拟数据过滤
    success1 = test_simple_filter()
    
    # 测试2: 真实数据搜索
    success2 = test_real_pythia_search()
    
    if success1 and success2:
        print(f"\n🎉 所有测试通过! 简化过滤配置工作正常")
        print(f"💡 建议: 现在可以重新运行自动报告发送器")
    elif success1:
        print(f"\n⚠️ 模拟测试通过，但真实搜索可能仍有问题")
        print(f"💡 建议: 检查API连接或进一步调整过滤条件")
    else:
        print(f"\n❌ 测试失败，请检查配置和代码")

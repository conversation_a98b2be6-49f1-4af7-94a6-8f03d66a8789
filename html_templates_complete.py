#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML模板管理器 - 完整版本
基于test_professional_report.html的完整模板系统
"""

from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import os


class HTMLTemplateManager:
    """HTML模板管理器 - 管理所有HTML模板"""
    
    def __init__(self):
        self.templates_dir = Path("templates")
        self.templates_dir.mkdir(exist_ok=True)
        
        # 初始化模板
        self._initialize_templates()
    
    def _initialize_templates(self):
        """初始化所有模板文件"""
        # 创建主报告模板
        self._create_main_report_template()
        # 创建错误页面模板
        self._create_error_template()
    
    def _create_main_report_template(self):
        """创建主报告HTML模板 - 完整版本"""
        # 从test_professional_report.html读取完整内容
        try:
            with open('test_professional_report.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除开头的多余字符
            if content.startswith('"""'):
                content = content[3:]
            
            # 替换硬编码的变量为模板变量
            template_content = content.replace(
                '{self.pythia_token_address[:12]}...', '{{token_address}}'
            ).replace(
                '{now.strftime(\'%Y-%m-%d %H:%M\')}', '{{timestamp}}'
            ).replace(
                '{factors_html}', '{{positive_factors}}'
            ).replace(
                '{avg_price_change:.2f}', '{{avg_price_change}}'
            ).replace(
                '{price_diff:.2f}', '{{price_diff}}'
            ).replace(
                '{(total_buys + total_sells) / len(pairs_data) if pairs_data else 0:.0f}', '{{trading_activity}}'
            ).replace(
                '{(total_liquidity / total_market_cap) if total_market_cap > 0 else 0:.3f}', '{{market_depth}}'
            ).replace(
                '{sentiment[\'sentiment\']}', '{{sentiment}}'
            ).replace(
                '{buy_sell_ratio:.2f}', '{{buy_sell_ratio}}'
            ).replace(
                '{total_market_cap/1000000:.2f}', '{{market_cap_millions}}'
            ).replace(
                '{total_volume/1000000:.2f}', '{{volume_24h_millions}}'
            ).replace(
                '{total_liquidity/1000000:.2f}', '{{liquidity_millions}}'
            ).replace(
                '{volume_to_mcap:.1f}', '{{volume_to_mcap}}'
            ).replace(
                '{pairs_table_html}', '{{pairs_table}}'
            ).replace(
                '{liquidity_html}', '{{liquidity_table}}'
            )
            
            template_path = self.templates_dir / "main_report.html"
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(template_content)
                
        except FileNotFoundError:
            print("⚠️ test_professional_report.html 文件未找到，使用备用模板")
            self._create_fallback_template()
    
    def _create_fallback_template(self):
        """创建备用模板"""
        template_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告</title>
    <style>
        body { font-family: Arial, sans-serif; background: #0D1117; color: #E6EDF3; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 20px; }
        .card { background: #161B22; border: 1px solid #30363D; border-radius: 8px; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐭 PYTHIA 分析报告</h1>
            <p>Token: {{token_address}} | Time: {{timestamp}}</p>
        </div>
        <div class="content">
            <div class="card">
                <h2>积极因素</h2>
                <ul>{{positive_factors}}</ul>
            </div>
            <div class="card">
                <h2>核心指标</h2>
                <p>市值: {{market_cap_millions}}M</p>
                <p>交易量: {{volume_24h_millions}}M</p>
                <p>流动性: {{liquidity_millions}}M</p>
            </div>
            <div class="card">
                <h2>交易对</h2>
                <table>{{pairs_table}}</table>
            </div>
        </div>
    </div>
</body>
</html>'''
        
        template_path = self.templates_dir / "main_report.html"
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
    
    def _create_error_template(self):
        """创建错误页面模板"""
        template_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #0D1117;
            color: #E6EDF3;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .error-container {
            background: #161B22;
            border: 1px solid #30363D;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #F59E0B;
        }
        .error-message {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #B0BAC6;
        }
        .retry-info {
            background: #0D1117;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #7D8590;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">{{error_icon}}</div>
        <h1 class="error-title">{{error_title}}</h1>
        <p class="error-message">{{error_message}}</p>
        <div class="retry-info">
            <strong>生成时间:</strong> {{timestamp}}<br>
            <strong>状态:</strong> {{status}}<br>
            <strong>建议:</strong> {{suggestion}}
        </div>
    </div>
</body>
</html>'''
        
        template_path = self.templates_dir / "error_page.html"
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
    
    def load_template(self, template_name: str) -> str:
        """加载指定的模板文件"""
        template_path = self.templates_dir / f"{template_name}.html"
        if not template_path.exists():
            raise FileNotFoundError(f"模板文件不存在: {template_path}")
        
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def render_template(self, template_name: str, data: Dict[str, Any]) -> str:
        """渲染模板，填充数据"""
        template_content = self.load_template(template_name)
        
        # 简单的模板变量替换
        for key, value in data.items():
            placeholder = f"{{{{{key}}}}}"
            template_content = template_content.replace(placeholder, str(value))
        
        return template_content
    
    def generate_professional_report(self, report_data: Dict[str, Any]) -> str:
        """生成专业报告HTML"""
        # 准备模板数据
        template_data = {
            'token_address': report_data.get('token_address', '')[:12] + '...',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M'),
            'positive_factors': report_data.get('positive_factors', ''),
            'avg_price_change': f"{report_data.get('avg_price_change', 0):.2f}",
            'price_diff': f"{report_data.get('price_diff', 0):.2f}",
            'trading_activity': f"{report_data.get('trading_activity', 0):.0f}",
            'market_depth': f"{report_data.get('market_depth', 0):.3f}",
            'sentiment': report_data.get('sentiment', '😐 中性'),
            'buy_sell_ratio': f"{report_data.get('buy_sell_ratio', 0):.2f}",
            'market_cap_millions': f"{report_data.get('total_market_cap', 0)/1000000:.2f}",
            'volume_24h_millions': f"{report_data.get('total_volume', 0)/1000000:.2f}",
            'liquidity_millions': f"{report_data.get('total_liquidity', 0)/1000000:.2f}",
            'volume_to_mcap': f"{report_data.get('volume_to_mcap', 0):.1f}",
            'pairs_table': report_data.get('pairs_table', ''),
            'liquidity_table': report_data.get('liquidity_table', '')
        }
        
        return self.render_template('main_report', template_data)
    
    def generate_error_report(self, error_type: str = "network") -> str:
        """生成错误报告HTML"""
        error_configs = {
            "network": {
                "error_icon": "🔌",
                "error_title": "网络连接异常",
                "error_message": "无法连接到DexScreener API服务器。<br>这可能是由于网络连接问题或API服务暂时不可用。",
                "status": "自动重试机制已启用",
                "suggestion": "系统将在下个周期自动重试"
            },
            "data": {
                "error_icon": "📊",
                "error_title": "数据获取失败",
                "error_message": "无法获取有效的市场数据。<br>请稍后重试或检查代币地址是否正确。",
                "status": "数据源暂时不可用",
                "suggestion": "请稍后重试"
            },
            "general": {
                "error_icon": "⚠️",
                "error_title": "系统异常",
                "error_message": "系统遇到未知错误。<br>请联系技术支持或稍后重试。",
                "status": "错误已记录",
                "suggestion": "请联系技术支持"
            }
        }
        
        config = error_configs.get(error_type, error_configs["general"])
        template_data = {
            'title': 'PYTHIA 分析报告 - 错误',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            **config
        }
        
        return self.render_template('error_page', template_data)

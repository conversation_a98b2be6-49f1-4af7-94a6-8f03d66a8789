#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API响应数据
检查实际返回的数据结构
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_api_response():
    """调试API响应数据结构"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print("🔍 调试PYTHIA API响应数据")
        print("=" * 50)
        
        analyzer = PythiaIntegratedAnalyzer()
        
        # 直接调用搜索API，不经过过滤
        print("📡 直接调用API搜索...")
        
        # 搜索PYTHIA关键词
        raw_response = analyzer.search_pythia_pairs("PYTHIA")
        
        print(f"\n📊 原始API响应:")
        if raw_response:
            print(f"   响应类型: {type(raw_response)}")
            print(f"   响应键: {list(raw_response.keys()) if isinstance(raw_response, dict) else 'Not a dict'}")
            
            if isinstance(raw_response, dict) and "pairs" in raw_response:
                pairs = raw_response["pairs"]
                print(f"   交易对数量: {len(pairs)}")
                
                if len(pairs) > 0:
                    print(f"\n📋 第一个交易对的完整数据结构:")
                    first_pair = pairs[0]
                    print(json.dumps(first_pair, indent=2, default=str))
                    
                    print(f"\n🔍 关键字段检查:")
                    print(f"   marketCap: {first_pair.get('marketCap', 'MISSING')}")
                    print(f"   liquidity: {first_pair.get('liquidity', 'MISSING')}")
                    print(f"   volume: {first_pair.get('volume', 'MISSING')}")
                    print(f"   priceUsd: {first_pair.get('priceUsd', 'MISSING')}")
                    print(f"   dexId: {first_pair.get('dexId', 'MISSING')}")
                    
                    # 检查嵌套字段
                    if 'liquidity' in first_pair and isinstance(first_pair['liquidity'], dict):
                        print(f"   liquidity.usd: {first_pair['liquidity'].get('usd', 'MISSING')}")
                    
                    if 'volume' in first_pair and isinstance(first_pair['volume'], dict):
                        print(f"   volume.h24: {first_pair['volume'].get('h24', 'MISSING')}")
                    
                    # 手动检查过滤条件
                    print(f"\n🧪 手动过滤检查:")
                    market_cap = first_pair.get("marketCap", 0)
                    liquidity_usd = first_pair.get("liquidity", {}).get("usd", 0) if isinstance(first_pair.get("liquidity"), dict) else 0
                    volume_24h = first_pair.get("volume", {}).get("h24", 0) if isinstance(first_pair.get("volume"), dict) else 0
                    price = float(first_pair.get("priceUsd", 0)) if first_pair.get("priceUsd") else 0
                    
                    print(f"   市值: {market_cap} (阈值: 0.01)")
                    print(f"   流动性: {liquidity_usd} (阈值: 0.01)")
                    print(f"   交易量: {volume_24h} (阈值: 0.01)")
                    print(f"   价格: {price} (阈值: 0.000000000001)")
                    
                    # 检查每个条件
                    print(f"\n✅ 过滤条件检查:")
                    print(f"   市值检查: {'通过' if market_cap >= 0.01 else '失败'}")
                    print(f"   流动性检查: {'通过' if liquidity_usd >= 0.01 else '失败'}")
                    print(f"   交易量检查: {'通过' if volume_24h >= 0.01 else '失败'}")
                    print(f"   价格检查: {'通过' if price >= 0.000000000001 else '失败'}")
                    
                    # 检查所有交易对的统计
                    print(f"\n📊 所有交易对统计:")
                    market_caps = [p.get('marketCap', 0) for p in pairs]
                    liquidities = [p.get('liquidity', {}).get('usd', 0) if isinstance(p.get('liquidity'), dict) else 0 for p in pairs]
                    volumes = [p.get('volume', {}).get('h24', 0) if isinstance(p.get('volume'), dict) else 0 for p in pairs]
                    
                    print(f"   市值范围: {min(market_caps):.2f} - {max(market_caps):.2f}")
                    print(f"   流动性范围: {min(liquidities):.2f} - {max(liquidities):.2f}")
                    print(f"   交易量范围: {min(volumes):.2f} - {max(volumes):.2f}")
                    
                    # 统计有多少交易对满足各个条件
                    market_cap_pass = sum(1 for mc in market_caps if mc >= 0.01)
                    liquidity_pass = sum(1 for lq in liquidities if lq >= 0.01)
                    volume_pass = sum(1 for vol in volumes if vol >= 0.01)
                    
                    print(f"\n📈 条件通过统计:")
                    print(f"   市值通过: {market_cap_pass}/{len(pairs)}")
                    print(f"   流动性通过: {liquidity_pass}/{len(pairs)}")
                    print(f"   交易量通过: {volume_pass}/{len(pairs)}")
                    
                else:
                    print("   ❌ 没有找到交易对数据")
            else:
                print("   ❌ 响应中没有'pairs'字段")
                print(f"   完整响应: {raw_response}")
        else:
            print("   ❌ API响应为空")
            
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_filter():
    """测试完全不过滤的情况"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print(f"\n🚫 测试完全不过滤")
        print("=" * 30)
        
        analyzer = PythiaIntegratedAnalyzer()
        
        # 临时修改过滤函数，返回所有数据
        def no_filter(pairs):
            return pairs, {
                "total_pairs": len(pairs),
                "final_pairs": len(pairs),
                "filtered_by_market_cap": 0,
                "filtered_by_liquidity": 0,
                "filtered_by_volume": 0,
                "filtered_by_price": 0
            }
        
        # 备份原始过滤函数
        original_filter = analyzer.filter_pairs_by_criteria
        analyzer.filter_pairs_by_criteria = no_filter
        
        try:
            # 搜索交易对
            pairs_data, filter_stats = analyzer.search_all_pythia_pairs()
            
            print(f"📊 无过滤搜索结果:")
            print(f"   找到交易对: {len(pairs_data)}个")
            
            if len(pairs_data) > 0:
                print(f"   ✅ 成功! API能够返回数据")
                print(f"   问题在于过滤条件过于严格")
                
                # 显示前3个交易对的关键信息
                print(f"\n📋 前3个交易对信息:")
                for i, pair in enumerate(pairs_data[:3], 1):
                    print(f"   {i}. 市值: {pair.get('marketCap', 'N/A')}")
                    print(f"      流动性: {pair.get('liquidity', {}).get('usd', 'N/A')}")
                    print(f"      交易量: {pair.get('volume', {}).get('h24', 'N/A')}")
                    print(f"      价格: {pair.get('priceUsd', 'N/A')}")
            else:
                print(f"   ❌ 即使不过滤也没有数据，可能是API问题")
                
        finally:
            # 恢复原始过滤函数
            analyzer.filter_pairs_by_criteria = original_filter
            
        return len(pairs_data) > 0
        
    except Exception as e:
        print(f"❌ 无过滤测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 PYTHIA API响应调试工具")
    print("=" * 60)
    
    # 调试API响应
    debug_success = debug_api_response()
    
    # 测试无过滤
    no_filter_success = test_no_filter()
    
    if debug_success and no_filter_success:
        print(f"\n💡 建议:")
        print(f"   1. 根据实际数据调整过滤阈值")
        print(f"   2. 检查数据字段的实际结构")
        print(f"   3. 考虑使用更宽松的过滤条件")
    elif debug_success:
        print(f"\n⚠️ API有响应但可能存在数据格式问题")
    else:
        print(f"\n❌ API调试失败，请检查网络连接和API配置")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整HTML模板系统测试脚本
测试基于test_professional_report.html的完整模板系统
"""

from html_templates_complete import HTMLTemplateManager
from datetime import datetime
import os


def test_complete_template_system():
    """测试完整的HTML模板系统"""
    print("🧪 测试完整HTML模板系统")
    print("=" * 60)
    
    # 初始化模板管理器
    template_manager = HTMLTemplateManager()
    
    # 测试1: 检查模板文件创建
    print("📁 检查模板文件...")
    templates_dir = template_manager.templates_dir
    
    expected_files = [
        "main_report.html",
        "error_page.html"
    ]
    
    for file_name in expected_files:
        file_path = templates_dir / file_name
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"   ✅ {file_name} - 存在 ({size} 字节)")
        else:
            print(f"   ❌ {file_name} - 不存在")
    
    # 测试2: 生成完整专业报告
    print("\n📊 测试完整专业报告生成...")
    try:
        # 准备完整的测试数据
        test_data = {
            'token_address': 'BQ5jRdxkppHviiqkZSzM7t4CsJaXVjNBuRwVgvn8pump',
            'positive_factors': '''
                <li>🚀 强劲的买入压力: 买卖比例2.8:1</li>
                <li>📈 高交易活跃度: 日交易量$25M</li>
                <li>🔄 多DEX支持: 分布在8个DEX</li>
                <li>💎 持续增长的流动性池</li>
                <li>🌟 社区活跃度持续上升</li>
                <li>⚡ 技术指标强劲</li>
            ''',
            'avg_price_change': 12.45,
            'price_diff': 8.32,
            'trading_activity': 156,
            'market_depth': 0.085,
            'buy_sell_ratio': 2.8,
            'sentiment': '🚀 极度乐观',
            'volume_to_mcap': 28.5,
            'total_market_cap': 125000000,   # 125M
            'total_volume': 25000000,        # 25M
            'total_liquidity': 8500000,      # 8.5M
            'pairs_count': 28,
            'pairs_table': '''
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: #7D8590;">Raydium</small></td>
                    <td>$0.000189</td>
                    <td style="color: #28A745;">+12.45%</td>
                    <td>$8.2M</td>
                </tr>
                <tr>
                    <td>PYTHIA/USDC<br><small style="color: #7D8590;">Orca</small></td>
                    <td>$0.000191</td>
                    <td style="color: #28A745;">+11.23%</td>
                    <td>$6.1M</td>
                </tr>
                <tr>
                    <td>PYTHIA/USDT<br><small style="color: #7D8590;">Jupiter</small></td>
                    <td>$0.000187</td>
                    <td style="color: #28A745;">+13.67%</td>
                    <td>$4.8M</td>
                </tr>
                <tr>
                    <td>PYTHIA/RAY<br><small style="color: #7D8590;">Raydium</small></td>
                    <td>$0.000185</td>
                    <td style="color: #28A745;">+9.45%</td>
                    <td>$3.2M</td>
                </tr>
                <tr>
                    <td>PYTHIA/BONK<br><small style="color: #7D8590;">Meteora</small></td>
                    <td>$0.000192</td>
                    <td style="color: #28A745;">+15.23%</td>
                    <td>$2.7M</td>
                </tr>
            ''',
            'liquidity_table': '''
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: #7D8590;">Raydium</small></td>
                    <td>$3.2M</td>
                    <td>37.6%</td>
                </tr>
                <tr>
                    <td>PYTHIA/USDC<br><small style="color: #7D8590;">Orca</small></td>
                    <td>$2.1M</td>
                    <td>24.7%</td>
                </tr>
                <tr>
                    <td>PYTHIA/USDT<br><small style="color: #7D8590;">Jupiter</small></td>
                    <td>$1.5M</td>
                    <td>17.6%</td>
                </tr>
                <tr>
                    <td>PYTHIA/RAY<br><small style="color: #7D8590;">Raydium</small></td>
                    <td>$1.0M</td>
                    <td>11.8%</td>
                </tr>
                <tr>
                    <td>PYTHIA/BONK<br><small style="color: #7D8590;">Meteora</small></td>
                    <td>$0.7M</td>
                    <td>8.3%</td>
                </tr>
            '''
        }
        
        # 生成完整报告
        professional_html = template_manager.generate_professional_report(test_data)
        
        if professional_html and "PYTHIA" in professional_html:
            print("   ✅ 完整专业报告生成成功")
            
            # 保存完整报告测试
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            test_report_path = f"complete_professional_report_{timestamp}.html"
            with open(test_report_path, 'w', encoding='utf-8') as f:
                f.write(professional_html)
            print(f"   💾 完整报告已保存到: {test_report_path}")
            
            # 检查关键元素
            key_elements = [
                "PYTHIA</span>分析报告",
                "积极因素",
                "增强指标", 
                "价格一致性",
                "交易活跃度",
                "市场深度",
                "情绪指数",
                "买卖比",
                "价格走势图",
                "交易对详情",
                "流动性分布",
                "TradingView"
            ]
            
            print("   🔍 检查关键元素:")
            missing_elements = []
            for element in key_elements:
                if element in professional_html:
                    print(f"      ✅ {element}")
                else:
                    print(f"      ❌ {element}")
                    missing_elements.append(element)
            
            if not missing_elements:
                print("   🎉 所有关键元素都存在！")
            else:
                print(f"   ⚠️ 缺失元素: {', '.join(missing_elements)}")
                
            # 检查文件大小
            file_size = os.path.getsize(test_report_path)
            print(f"   📏 文件大小: {file_size} 字节")
            
            if file_size > 10000:  # 至少10KB
                print("   ✅ 文件大小合理")
            else:
                print("   ⚠️ 文件可能不完整")
                
            return test_report_path
            
        else:
            print("   ❌ 完整专业报告生成失败")
            return None
            
    except Exception as e:
        print(f"   ❌ 完整专业报告生成异常: {e}")
        return None
    
    # 测试3: 生成错误报告
    print("\n🔌 测试错误报告生成...")
    try:
        error_types = ["network", "data", "general"]
        for error_type in error_types:
            error_html = template_manager.generate_error_report(error_type)
            if error_html:
                error_filename = f"complete_error_{error_type}.html"
                with open(error_filename, 'w', encoding='utf-8') as f:
                    f.write(error_html)
                print(f"   ✅ {error_type}错误报告: {error_filename}")
            else:
                print(f"   ❌ {error_type}错误报告生成失败")
                
    except Exception as e:
        print(f"   ❌ 错误报告生成异常: {e}")


def main():
    """主函数"""
    print("🎯 完整HTML模板系统测试")
    print("基于 test_professional_report.html 的完整模板")
    print("=" * 70)
    
    # 运行测试
    report_file = test_complete_template_system()
    
    print("\n" + "=" * 70)
    print("🎉 完整模板系统测试完成!")
    
    if report_file:
        print(f"\n🌟 主要成果:")
        print(f"   - 生成了完整的专业报告: {report_file}")
        print(f"   - 创建了所有类型的错误报告")
        print(f"   - 验证了模板系统的完整性")
        
        print(f"\n📖 下一步:")
        print(f"   1. 在浏览器中打开 {report_file} 查看效果")
        print(f"   2. 对比原始 test_professional_report.html 确认一致性")
        print(f"   3. 在主程序中使用新的模板系统")
        print(f"   4. 所有硬编码HTML已成功提取为模块化模板")
    else:
        print("\n❌ 测试过程中遇到问题，请检查错误信息")


if __name__ == "__main__":
    main()

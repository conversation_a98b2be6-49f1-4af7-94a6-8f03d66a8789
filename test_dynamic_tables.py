#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态表格大小调整功能
验证表格能够自适应填满模块界面
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dynamic_table_layout():
    """测试动态表格布局"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print("📊 测试动态表格布局功能")
        print("=" * 50)
        
        # 创建分析器实例
        analyzer = PythiaIntegratedAnalyzer()
        
        print("🔄 生成测试报告...")
        report_content = analyzer.generate_professional_report()
        
        if report_content:
            # 保存测试报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_dynamic_tables_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"✅ 测试报告已生成: {filename}")
            
            # 分析报告内容
            print(f"\n📋 报告内容分析:")
            print(f"   报告长度: {len(report_content):,} 字符")
            
            # 检查CSS样式
            css_features = [
                ("动态表格模块", "table-module" in report_content),
                ("自适应容器", "table-container" in report_content),
                ("响应式表格", "data-table" in report_content),
                ("交易对单元格", "pair-cell" in report_content),
                ("数值单元格", "number-cell" in report_content),
                ("百分比单元格", "percent-cell" in report_content),
                ("响应式媒体查询", "@media" in report_content),
                ("Flexbox布局", "flex-grow" in report_content)
            ]
            
            print(f"\n🎨 CSS功能检查:")
            for feature_name, exists in css_features:
                status = "✅" if exists else "❌"
                print(f"   {status} {feature_name}")
            
            # 检查表格数据
            table_features = [
                ("主要交易对表格", "💰 主要交易对" in report_content),
                ("流动性分布表格", "💧 流动性分布" in report_content),
                ("交易对名称格式", "pair-name" in report_content),
                ("DEX标识格式", "pair-dex" in report_content),
                ("格式化价格", "number-cell" in report_content),
                ("格式化百分比", "percent-cell" in report_content)
            ]
            
            print(f"\n📊 表格功能检查:")
            for feature_name, exists in table_features:
                status = "✅" if exists else "❌"
                print(f"   {status} {feature_name}")
            
            # 检查布局结构
            layout_features = [
                ("左栏布局", "left-column" in report_content),
                ("中栏布局", "center-column" in report_content),
                ("右栏布局", "right-column" in report_content),
                ("模块卡片", "module-card" in report_content),
                ("表格模块", "table-module" in report_content)
            ]
            
            print(f"\n🏗️ 布局结构检查:")
            for feature_name, exists in layout_features:
                status = "✅" if exists else "❌"
                print(f"   {status} {feature_name}")
            
            # 统计通过的功能
            all_features = css_features + table_features + layout_features
            passed_count = sum(1 for _, exists in all_features if exists)
            total_count = len(all_features)
            
            print(f"\n📈 功能完成度:")
            print(f"   通过功能: {passed_count}/{total_count}")
            print(f"   完成率: {(passed_count/total_count*100):.1f}%")
            
            if passed_count >= total_count * 0.8:
                print(f"   🎉 优秀! 动态表格功能基本完成")
            elif passed_count >= total_count * 0.6:
                print(f"   👍 良好! 大部分功能正常")
            else:
                print(f"   ⚠️ 需要改进，部分功能缺失")
            
            print(f"\n🌐 查看报告:")
            print(f"   文件路径: {os.path.abspath(filename)}")
            print(f"   浏览器打开: file://{os.path.abspath(filename)}")
            
            return True
        else:
            print("❌ 报告生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_responsiveness():
    """测试表格响应式特性"""
    print(f"\n📱 测试表格响应式特性")
    print("=" * 40)
    
    responsive_features = [
        "自适应列宽",
        "文本溢出处理", 
        "移动端优化",
        "字体大小调整",
        "间距自适应",
        "滚动条处理"
    ]
    
    print("🎯 响应式特性清单:")
    for i, feature in enumerate(responsive_features, 1):
        print(f"   {i}. {feature}")
    
    print(f"\n💡 测试建议:")
    print(f"   1. 在不同屏幕尺寸下查看报告")
    print(f"   2. 检查表格是否填满模块空间")
    print(f"   3. 验证文本是否正确换行")
    print(f"   4. 确认滚动条正常工作")
    print(f"   5. 测试移动设备兼容性")

def show_layout_summary():
    """显示布局优化总结"""
    print(f"\n📋 动态表格布局优化总结")
    print("=" * 50)
    
    print("✅ 已实现的优化:")
    print("   • 表格模块使用 flex-grow 自动填充空间")
    print("   • 表格容器支持垂直滚动")
    print("   • 交易对单元格优化显示格式")
    print("   • 数值和百分比右对齐显示")
    print("   • 响应式字体和间距调整")
    print("   • 文本溢出省略号处理")
    print("   • 表头固定位置")
    print("   • 悬停效果增强交互")
    
    print(f"\n🎨 样式特性:")
    print("   • pair-cell: 交易对信息分行显示")
    print("   • number-cell: 数值右对齐，等宽字体")
    print("   • percent-cell: 百分比加粗显示")
    print("   • 响应式媒体查询适配小屏幕")
    
    print(f"\n📊 表格内容:")
    print("   • 主要交易对: 显示价格、变化、交易量")
    print("   • 流动性分布: 显示流动性、占比")
    print("   • 智能数值格式化 (M/K/原值)")
    print("   • DEX信息小字体显示")
    
    print(f"\n🚀 性能优化:")
    print("   • CSS Grid 和 Flexbox 高效布局")
    print("   • 最小化重排和重绘")
    print("   • 优化渲染性能")

if __name__ == "__main__":
    print("📊 PYTHIA动态表格测试工具")
    print("=" * 60)
    
    # 显示优化总结
    show_layout_summary()
    
    # 测试动态表格布局
    success = test_dynamic_table_layout()
    
    # 测试响应式特性
    test_table_responsiveness()
    
    if success:
        print(f"\n🎉 动态表格测试完成!")
        print(f"💡 请在浏览器中查看生成的测试报告")
        print(f"🔍 验证表格是否正确填满模块界面")
    else:
        print(f"\n❌ 测试失败，请检查代码")

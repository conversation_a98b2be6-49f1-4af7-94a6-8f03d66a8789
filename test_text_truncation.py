#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文本截断修复
验证价格、百分比、交易量是否完整显示
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_text_truncation_fix():
    """测试文本截断修复"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print("🔍 测试文本截断修复")
        print("=" * 50)
        
        # 创建分析器实例
        analyzer = PythiaIntegratedAnalyzer()
        
        print("🔄 生成截断测试报告...")
        report_content = analyzer.generate_professional_report()
        
        if report_content:
            # 保存测试报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_truncation_fix_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"✅ 截断测试报告已生成: {filename}")
            
            # 检查文本截断修复相关的CSS
            truncation_fixes = [
                ("移除text-overflow", "text-overflow: ellipsis" not in report_content),
                ("移除overflow hidden", "overflow: hidden" not in report_content or "overflow: visible" in report_content),
                ("添加word-wrap", "word-wrap: break-word" in report_content),
                ("添加white-space nowrap", "white-space: nowrap" in report_content),
                ("调整列宽", "width: 35%" in report_content),
                ("价格列宽度", "width: 25%" in report_content),
                ("百分比列宽度", "width: 20%" in report_content),
                ("交易量列宽度", "width: 20%" in report_content)
            ]
            
            print(f"\n🔧 文本截断修复检查:")
            for feature_name, fixed in truncation_fixes:
                status = "✅" if fixed else "❌"
                print(f"   {status} {feature_name}")
            
            # 检查表格内容是否包含完整数据
            content_checks = [
                ("完整价格格式", "$0.0" in report_content and "..." not in report_content.split("$0.0")[1][:10] if "$0.0" in report_content else False),
                ("完整百分比", "%" in report_content and "-" in report_content),
                ("完整交易量", "M" in report_content or "K" in report_content),
                ("交易对名称", "PYTHIA/SOL" in report_content),
                ("DEX标识", "meteora" in report_content or "raydium" in report_content),
                ("流动性数据", "$9." in report_content or "$2." in report_content),
                ("占比数据", "76." in report_content or "23." in report_content)
            ]
            
            print(f"\n📊 内容完整性检查:")
            for feature_name, complete in content_checks:
                status = "✅" if complete else "❌"
                print(f"   {status} {feature_name}")
            
            # 分析可能的截断模式
            truncation_patterns = [
                ("价格截断", "$0.09..." in report_content),
                ("百分比截断", "-9..." in report_content),
                ("交易量截断", "$6.5..." in report_content),
                ("流动性截断", "$9.5..." in report_content),
                ("占比截断", "76..." in report_content)
            ]
            
            print(f"\n⚠️ 截断模式检查:")
            truncation_found = False
            for pattern_name, found in truncation_patterns:
                if found:
                    print(f"   ❌ 发现截断: {pattern_name}")
                    truncation_found = True
                else:
                    print(f"   ✅ 无截断: {pattern_name}")
            
            if not truncation_found:
                print(f"\n🎉 所有文本截断问题已修复!")
            else:
                print(f"\n⚠️ 仍存在文本截断问题")
            
            # 统计修复效果
            all_fixes = truncation_fixes + content_checks
            fixed_count = sum(1 for _, fixed in all_fixes if fixed)
            total_count = len(all_fixes)
            
            print(f"\n📈 修复完成度:")
            print(f"   修复项目: {fixed_count}/{total_count}")
            print(f"   修复率: {(fixed_count/total_count*100):.1f}%")
            
            if fixed_count >= total_count * 0.9:
                print(f"   🎉 优秀! 文本截断问题基本解决")
            elif fixed_count >= total_count * 0.7:
                print(f"   👍 良好! 大部分截断问题已修复")
            else:
                print(f"   ⚠️ 需要进一步优化")
            
            print(f"\n🌐 查看修复效果:")
            print(f"   文件路径: {os.path.abspath(filename)}")
            print(f"   浏览器打开: file://{os.path.abspath(filename)}")
            
            return not truncation_found
        else:
            print("❌ 报告生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_truncation_analysis():
    """显示截断问题分析"""
    print(f"\n📋 文本截断问题分析")
    print("=" * 50)
    
    print("🔍 原始问题:")
    print("   • 价格显示: $0.09... (应显示 $0.091150)")
    print("   • 百分比显示: -9... (应显示 -9.23%)")
    print("   • 交易量显示: $6.5... (应显示 $6.5M)")
    print("   • 流动性显示: $9.5... (应显示 $9.53M)")
    
    print(f"\n🔧 修复措施:")
    print("   • 移除 text-overflow: ellipsis")
    print("   • 移除 overflow: hidden")
    print("   • 添加 word-wrap: break-word")
    print("   • 添加 white-space: nowrap 给数值列")
    print("   • 调整列宽比例避免挤压")
    print("   • 设置 overflow: visible")
    
    print(f"\n📏 新的列宽设置:")
    print("   主要交易对表格:")
    print("     - 交易对列: 35% (原40%)")
    print("     - 价格列: 25% (不变)")
    print("     - 24h%列: 20% (原15%)")
    print("     - 交易量列: 20% (不变)")
    print("   流动性分布表格:")
    print("     - 交易对列: 45% (原50%)")
    print("     - 流动性列: 30% (不变)")
    print("     - 占比列: 25% (原20%)")

def test_expected_display():
    """测试期望的显示效果"""
    print(f"\n🎯 期望的显示效果")
    print("=" * 40)
    
    expected_data = [
        {
            "table": "主要交易对",
            "rows": [
                ["PYTHIA/SOL\nmeteora", "$0.091150", "-9.23%", "$6.52M"],
                ["PYTHIA/SOL\nraydium", "$0.089003", "-8.63%", "$140.1K"]
            ]
        },
        {
            "table": "流动性分布", 
            "rows": [
                ["PYTHIA/SOL\nmeteora", "$9.53M", "76.5%"],
                ["PYTHIA/SOL\nraydium", "$2.93M", "23.5%"]
            ]
        }
    ]
    
    for table_data in expected_data:
        print(f"\n📊 {table_data['table']}:")
        print("   " + "-" * 50)
        for row in table_data['rows']:
            formatted_row = " | ".join(f"{cell:>12}" for cell in row)
            print(f"   {formatted_row}")

if __name__ == "__main__":
    print("🔍 PYTHIA文本截断修复测试工具")
    print("=" * 60)
    
    # 显示截断问题分析
    show_truncation_analysis()
    
    # 测试文本截断修复
    success = test_text_truncation_fix()
    
    # 显示期望效果
    test_expected_display()
    
    if success:
        print(f"\n🎉 文本截断修复测试完成!")
        print(f"💡 请在浏览器中查看生成的测试报告")
        print(f"🔍 验证所有文本是否完整显示")
    else:
        print(f"\n❌ 仍存在文本截断问题，需要进一步调整")

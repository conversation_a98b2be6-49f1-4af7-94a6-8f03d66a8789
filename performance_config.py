#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PYTHIA性能优化配置
包含各种性能优化设置
"""

# ================================
# 性能优化配置
# ================================

PERFORMANCE_CONFIG = {
    # API请求优化
    "api_optimization": {
        "enable_caching": True,           # 启用缓存
        "cache_timeout": 300,             # 缓存超时时间(秒) - 5分钟
        "max_concurrent_requests": 3,     # 最大并发请求数
        "request_timeout": 15,            # 请求超时时间(秒) - 从30秒优化到15秒
        "retry_attempts": 3,              # 重试次数 - 从5次优化到3次
        "retry_delay": 2,                 # 重试延迟(秒) - 从3秒优化到2秒
        "connection_pool_size": 10,       # 连接池大小
        "keep_alive": True,               # 保持连接活跃
    },
    
    # HTML转图片优化
    "html_to_image": {
        "chart_wait_time": 20,            # 图表等待时间(秒) - 从60秒优化到20秒
        "render_check_rounds": 2,         # 渲染检查轮数 - 从3轮优化到2轮
        "check_interval": 2,              # 检查间隔(秒) - 从3秒优化到2秒
        "enable_smart_cropping": True,    # 启用智能裁剪
        "auto_size_detection": True,      # 启用自动尺寸检测
        "max_retries": 2,                 # 最大重试次数 - 从3次优化到2次
    },
    
    # 数据处理优化
    "data_processing": {
        "enable_parallel_analysis": True, # 启用并行分析
        "max_pairs_to_analyze": 50,       # 最大分析交易对数 - 限制数量提高速度
        "enable_data_filtering": True,    # 启用数据过滤
        "filter_low_volume": True,        # 过滤低交易量交易对
        "min_volume_threshold": 1000,     # 最小交易量阈值
    },
    
    # 报告生成优化
    "report_generation": {
        "enable_template_caching": True,  # 启用模板缓存
        "compress_html": False,           # HTML压缩 - 可能影响可读性
        "optimize_css": True,             # CSS优化
        "reduce_precision": True,         # 减少数字精度
        "max_pairs_in_table": 10,         # 表格中最大交易对数
    },
    
    # 监控和调试
    "monitoring": {
        "enable_performance_logging": True,  # 启用性能日志
        "log_slow_operations": True,         # 记录慢操作
        "slow_operation_threshold": 5.0,     # 慢操作阈值(秒)
        "enable_memory_monitoring": False,   # 启用内存监控
        "enable_timing_stats": True,         # 启用时间统计
    }
}

# ================================
# 快速模式配置
# ================================

FAST_MODE_CONFIG = {
    # 快速模式 - 牺牲一些准确性换取速度
    "api_optimization": {
        "enable_caching": True,
        "cache_timeout": 600,             # 更长的缓存时间
        "max_concurrent_requests": 5,     # 更多并发请求
        "request_timeout": 10,            # 更短的超时时间
        "retry_attempts": 2,              # 更少的重试
        "retry_delay": 1,                 # 更短的重试延迟
    },
    
    "html_to_image": {
        "chart_wait_time": 10,            # 更短的等待时间
        "render_check_rounds": 1,         # 只检查1轮
        "check_interval": 1,              # 更短的检查间隔
        "max_retries": 1,                 # 只重试1次
    },
    
    "data_processing": {
        "max_pairs_to_analyze": 20,       # 分析更少的交易对
        "min_volume_threshold": 5000,     # 更高的过滤阈值
    },
    
    "report_generation": {
        "max_pairs_in_table": 5,          # 表格中显示更少交易对
        "reduce_precision": True,
    }
}

# ================================
# 质量模式配置
# ================================

QUALITY_MODE_CONFIG = {
    # 质量模式 - 优先保证准确性和完整性
    "api_optimization": {
        "enable_caching": True,
        "cache_timeout": 180,             # 较短的缓存时间保证数据新鲜
        "max_concurrent_requests": 2,     # 较少并发避免限制
        "request_timeout": 30,            # 较长超时时间
        "retry_attempts": 5,              # 更多重试保证成功
        "retry_delay": 3,                 # 较长重试延迟
    },
    
    "html_to_image": {
        "chart_wait_time": 30,            # 更长等待时间保证图表完全加载
        "render_check_rounds": 3,         # 更多检查轮数
        "check_interval": 3,              # 较长检查间隔
        "max_retries": 3,                 # 更多重试
    },
    
    "data_processing": {
        "max_pairs_to_analyze": 100,      # 分析更多交易对
        "min_volume_threshold": 100,      # 较低过滤阈值
    },
    
    "report_generation": {
        "max_pairs_in_table": 20,         # 显示更多交易对
        "reduce_precision": False,        # 保持完整精度
    }
}

# ================================
# 性能监控函数
# ================================

def get_performance_config(mode="balanced"):
    """
    获取性能配置
    
    Args:
        mode: 模式选择 ("fast", "balanced", "quality")
    
    Returns:
        dict: 性能配置字典
    """
    if mode == "fast":
        return FAST_MODE_CONFIG
    elif mode == "quality":
        return QUALITY_MODE_CONFIG
    else:
        return PERFORMANCE_CONFIG

def apply_performance_config(analyzer, mode="balanced"):
    """
    应用性能配置到分析器
    
    Args:
        analyzer: PythiaIntegratedAnalyzer实例
        mode: 性能模式
    """
    config = get_performance_config(mode)
    
    # 应用API优化配置
    api_config = config["api_optimization"]
    analyzer.timeout = api_config["request_timeout"]
    analyzer.retry_attempts = api_config["retry_attempts"]
    analyzer.retry_delay = api_config["retry_delay"]
    
    if hasattr(analyzer, 'cache_timeout'):
        analyzer.cache_timeout = api_config["cache_timeout"]
    
    # 应用HTML转图片优化配置
    html_config = config["html_to_image"]
    if hasattr(analyzer.html_converter, 'chart_wait_time'):
        analyzer.html_converter.chart_wait_time = html_config["chart_wait_time"]
    
    print(f"✅ 已应用 {mode} 模式性能配置")

# ================================
# 性能测试工具
# ================================

def benchmark_mode(analyzer, mode_name, config):
    """
    基准测试特定模式的性能
    
    Args:
        analyzer: 分析器实例
        mode_name: 模式名称
        config: 配置字典
    
    Returns:
        dict: 性能统计结果
    """
    import time
    
    print(f"\n🔬 测试 {mode_name} 模式性能...")
    
    # 应用配置
    apply_performance_config(analyzer, mode_name)
    
    start_time = time.time()
    
    try:
        # 执行完整流程
        pairs_data, _ = analyzer.search_all_pythia_pairs()
        report = analyzer.generate_professional_report()
        
        total_time = time.time() - start_time
        
        return {
            "mode": mode_name,
            "total_time": total_time,
            "pairs_found": len(pairs_data) if pairs_data else 0,
            "report_length": len(report) if report else 0,
            "success": True
        }
    except Exception as e:
        return {
            "mode": mode_name,
            "total_time": time.time() - start_time,
            "error": str(e),
            "success": False
        }

if __name__ == "__main__":
    print("🚀 PYTHIA性能配置模块")
    print("=" * 40)
    print("可用模式:")
    print("  • fast - 快速模式")
    print("  • balanced - 平衡模式 (默认)")
    print("  • quality - 质量模式")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表格对齐功能
验证数据和字段是否正确对齐
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_table_alignment():
    """测试表格对齐功能"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print("📊 测试表格对齐功能")
        print("=" * 50)
        
        # 创建分析器实例
        analyzer = PythiaIntegratedAnalyzer()
        
        print("🔄 生成对齐测试报告...")
        report_content = analyzer.generate_professional_report()
        
        if report_content:
            # 保存测试报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_alignment_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"✅ 对齐测试报告已生成: {filename}")
            
            # 检查表格对齐相关的CSS
            alignment_features = [
                ("固定表格布局", "table-layout: fixed" in report_content),
                ("交易对表格类", "pairs-table" in report_content),
                ("流动性表格类", "liquidity-table" in report_content),
                ("列宽设置", "width: 40%" in report_content),
                ("右对齐数值", "text-align: right" in report_content),
                ("左对齐交易对", "text-align: left" in report_content),
                ("重要性标记", "!important" in report_content),
                ("等宽字体", "JetBrains Mono" in report_content)
            ]
            
            print(f"\n🎯 表格对齐功能检查:")
            for feature_name, exists in alignment_features:
                status = "✅" if exists else "❌"
                print(f"   {status} {feature_name}")
            
            # 检查表格结构
            table_structure = [
                ("主要交易对表头", "<th>交易对</th><th>价格</th><th>24h%</th><th>交易量</th>" in report_content),
                ("流动性分布表头", "<th>交易对</th><th>流动性</th><th>占比</th>" in report_content),
                ("交易对单元格", "pair-cell" in report_content),
                ("数值单元格", "number-cell" in report_content),
                ("百分比单元格", "percent-cell" in report_content),
                ("交易对名称", "pair-name" in report_content),
                ("DEX标识", "pair-dex" in report_content)
            ]
            
            print(f"\n📋 表格结构检查:")
            for feature_name, exists in table_structure:
                status = "✅" if exists else "❌"
                print(f"   {status} {feature_name}")
            
            # 分析CSS列宽设置
            column_widths = []
            if "width: 40%" in report_content:
                column_widths.append("交易对列: 40%")
            if "width: 25%" in report_content:
                column_widths.append("价格列: 25%")
            if "width: 15%" in report_content:
                column_widths.append("变化列: 15%")
            if "width: 20%" in report_content:
                column_widths.append("交易量列: 20%")
            if "width: 50%" in report_content:
                column_widths.append("流动性交易对列: 50%")
            if "width: 30%" in report_content:
                column_widths.append("流动性列: 30%")
            
            print(f"\n📏 列宽设置:")
            for width_setting in column_widths:
                print(f"   ✅ {width_setting}")
            
            # 统计通过的功能
            all_features = alignment_features + table_structure
            passed_count = sum(1 for _, exists in all_features if exists)
            total_count = len(all_features)
            
            print(f"\n📈 对齐功能完成度:")
            print(f"   通过功能: {passed_count}/{total_count}")
            print(f"   完成率: {(passed_count/total_count*100):.1f}%")
            
            if passed_count >= total_count * 0.9:
                print(f"   🎉 优秀! 表格对齐功能完美实现")
            elif passed_count >= total_count * 0.7:
                print(f"   👍 良好! 大部分对齐功能正常")
            else:
                print(f"   ⚠️ 需要改进，部分对齐功能缺失")
            
            print(f"\n🌐 查看对齐效果:")
            print(f"   文件路径: {os.path.abspath(filename)}")
            print(f"   浏览器打开: file://{os.path.abspath(filename)}")
            
            return True
        else:
            print("❌ 报告生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_alignment_summary():
    """显示对齐优化总结"""
    print(f"\n📋 表格对齐优化总结")
    print("=" * 50)
    
    print("✅ 对齐修复措施:")
    print("   • 使用 table-layout: fixed 固定表格布局")
    print("   • 为不同表格设置专用CSS类")
    print("   • 精确设置每列的宽度百分比")
    print("   • 统一设置文本对齐方式")
    print("   • 使用 !important 确保样式优先级")
    
    print(f"\n📊 主要交易对表格:")
    print("   • 交易对列: 40% 宽度，左对齐")
    print("   • 价格列: 25% 宽度，右对齐")
    print("   • 24h%列: 15% 宽度，右对齐")
    print("   • 交易量列: 20% 宽度，右对齐")
    
    print(f"\n💧 流动性分布表格:")
    print("   • 交易对列: 50% 宽度，左对齐")
    print("   • 流动性列: 30% 宽度，右对齐")
    print("   • 占比列: 20% 宽度，右对齐")
    
    print(f"\n🎨 样式优化:")
    print("   • 数值使用等宽字体 (JetBrains Mono)")
    print("   • 百分比加粗显示")
    print("   • 交易对信息分行显示")
    print("   • DEX标识小字体显示")

def test_specific_alignment_cases():
    """测试特定的对齐案例"""
    print(f"\n🔍 特定对齐案例测试")
    print("=" * 40)
    
    test_cases = [
        {
            "name": "长交易对名称",
            "example": "PYTHIA/SOL (meteora)",
            "expected": "名称和DEX分行显示，左对齐"
        },
        {
            "name": "小数价格",
            "example": "$0.091150",
            "expected": "右对齐，等宽字体"
        },
        {
            "name": "负百分比",
            "example": "-9.23%",
            "expected": "右对齐，红色显示"
        },
        {
            "name": "大交易量",
            "example": "$6.52M",
            "expected": "右对齐，智能单位"
        },
        {
            "name": "流动性占比",
            "example": "76.5%",
            "expected": "右对齐，加粗显示"
        }
    ]
    
    print("📋 测试案例:")
    for i, case in enumerate(test_cases, 1):
        print(f"   {i}. {case['name']}")
        print(f"      示例: {case['example']}")
        print(f"      期望: {case['expected']}")
        print()

if __name__ == "__main__":
    print("📊 PYTHIA表格对齐测试工具")
    print("=" * 60)
    
    # 显示对齐优化总结
    show_alignment_summary()
    
    # 测试表格对齐
    success = test_table_alignment()
    
    # 测试特定对齐案例
    test_specific_alignment_cases()
    
    if success:
        print(f"\n🎉 表格对齐测试完成!")
        print(f"💡 请在浏览器中查看生成的测试报告")
        print(f"🔍 验证数据和字段是否正确对齐")
    else:
        print(f"\n❌ 测试失败，请检查代码")

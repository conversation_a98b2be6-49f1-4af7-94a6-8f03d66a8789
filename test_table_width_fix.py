#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表格宽度和对齐修复
验证表格刚好适合宽度，流动性列左移
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_table_width_fix():
    """测试表格宽度和对齐修复"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print("📏 测试表格宽度和对齐修复")
        print("=" * 50)
        
        # 创建分析器实例
        analyzer = PythiaIntegratedAnalyzer()
        
        print("🔄 生成宽度修复测试报告...")
        report_content = analyzer.generate_professional_report()
        
        if report_content:
            # 保存测试报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_width_fix_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"✅ 宽度修复测试报告已生成: {filename}")
            
            # 检查滑块移除相关的CSS
            scrollbar_fixes = [
                ("移除overflow-x", "overflow-x: auto" not in report_content),
                ("移除overflow-y", "overflow-y: auto" not in report_content),
                ("保留flex布局", "display: flex" in report_content),
                ("保留flex-direction", "flex-direction: column" in report_content),
                ("保留flex-grow", "flex-grow: 1" in report_content)
            ]
            
            print(f"\n🚫 滑块移除检查:")
            for feature_name, fixed in scrollbar_fixes:
                status = "✅" if fixed else "❌"
                print(f"   {status} {feature_name}")
            
            # 检查流动性表格对齐修复
            liquidity_alignment = [
                ("流动性表格类", "liquidity-table" in report_content),
                ("交易对列50%", "width: 50%" in report_content),
                ("流动性列25%", "width: 25%" in report_content),
                ("占比列25%", "width: 25%" in report_content),
                ("流动性左对齐", "text-align: left" in report_content),
                ("占比左对齐", "text-align: left" in report_content),
                ("左边距设置", "padding-left: var(--spacing-xs)" in report_content)
            ]
            
            print(f"\n💧 流动性表格对齐检查:")
            for feature_name, aligned in liquidity_alignment:
                status = "✅" if aligned else "❌"
                print(f"   {status} {feature_name}")
            
            # 检查主要交易对表格宽度
            pairs_width = [
                ("交易对表格类", "pairs-table" in report_content),
                ("交易对列35%", "width: 35%" in report_content),
                ("价格列25%", "width: 25%" in report_content),
                ("百分比列20%", "width: 20%" in report_content),
                ("交易量列20%", "width: 20%" in report_content),
                ("固定表格布局", "table-layout: fixed" in report_content)
            ]
            
            print(f"\n💰 主要交易对表格宽度检查:")
            for feature_name, correct in pairs_width:
                status = "✅" if correct else "❌"
                print(f"   {status} {feature_name}")
            
            # 检查表格内容结构
            table_structure = [
                ("主要交易对标题", "💰 主要交易对" in report_content),
                ("流动性分布标题", "💧 流动性分布" in report_content),
                ("交易对数据", "PYTHIA/SOL" in report_content),
                ("DEX标识", "meteora" in report_content and "raydium" in report_content),
                ("价格数据", "$0.0" in report_content),
                ("百分比数据", "%" in report_content),
                ("交易量数据", "M" in report_content or "K" in report_content),
                ("流动性数据", "$9." in report_content or "$2." in report_content)
            ]
            
            print(f"\n📊 表格结构检查:")
            for feature_name, exists in table_structure:
                status = "✅" if exists else "❌"
                print(f"   {status} {feature_name}")
            
            # 统计修复效果
            all_fixes = scrollbar_fixes + liquidity_alignment + pairs_width + table_structure
            fixed_count = sum(1 for _, fixed in all_fixes if fixed)
            total_count = len(all_fixes)
            
            print(f"\n📈 宽度修复完成度:")
            print(f"   修复项目: {fixed_count}/{total_count}")
            print(f"   修复率: {(fixed_count/total_count*100):.1f}%")
            
            if fixed_count >= total_count * 0.9:
                print(f"   🎉 优秀! 表格宽度和对齐完美修复")
            elif fixed_count >= total_count * 0.7:
                print(f"   👍 良好! 大部分宽度问题已修复")
            else:
                print(f"   ⚠️ 需要进一步优化")
            
            print(f"\n🌐 查看修复效果:")
            print(f"   文件路径: {os.path.abspath(filename)}")
            print(f"   浏览器打开: file://{os.path.abspath(filename)}")
            
            return fixed_count >= total_count * 0.9
        else:
            print("❌ 报告生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_width_fix_summary():
    """显示宽度修复总结"""
    print(f"\n📋 表格宽度修复总结")
    print("=" * 50)
    
    print("🎯 修复目标:")
    print("   • 主要交易对表格: 不要滑块，刚好适合宽度")
    print("   • 流动性分布表格: 流动性和占比往左移一点")
    
    print(f"\n🔧 修复措施:")
    print("   • 移除 overflow-x: auto 和 overflow-y: auto")
    print("   • 保留 flex 布局确保适合宽度")
    print("   • 调整流动性表格列宽比例")
    print("   • 将流动性和占比列改为左对齐")
    print("   • 添加左边距让内容往左移")
    
    print(f"\n📏 新的列宽设置:")
    print("   主要交易对表格:")
    print("     - 交易对列: 35%")
    print("     - 价格列: 25%")
    print("     - 24h%列: 20%")
    print("     - 交易量列: 20%")
    print("   流动性分布表格:")
    print("     - 交易对列: 50%")
    print("     - 流动性列: 25% (左对齐)")
    print("     - 占比列: 25% (左对齐)")

def test_expected_layout():
    """测试期望的布局效果"""
    print(f"\n🎯 期望的布局效果")
    print("=" * 40)
    
    print("💰 主要交易对 (无滑块，刚好适合宽度):")
    print("   ┌─────────────────┬──────────┬────────┬──────────┐")
    print("   │ 交易对          │ 价格     │ 24h%   │ 交易量   │")
    print("   ├─────────────────┼──────────┼────────┼──────────┤")
    print("   │ PYTHIA/SOL      │ $0.09058 │ -9.50% │ $6.53M   │")
    print("   │ meteora         │          │        │          │")
    print("   ├─────────────────┼──────────┼────────┼──────────┤")
    print("   │ PYTHIA/SOL      │ $0.09047 │ -9.31% │ $140.1K  │")
    print("   │ raydium         │          │        │          │")
    print("   └─────────────────┴──────────┴────────┴──────────┘")
    
    print(f"\n💧 流动性分布 (流动性和占比往左移):")
    print("   ┌─────────────────────────┬──────────┬────────┐")
    print("   │ 交易对                  │ 流动性   │ 占比   │")
    print("   ├─────────────────────────┼──────────┼────────┤")
    print("   │ PYTHIA/SOL              │$9.54M    │76.5%   │")
    print("   │ meteora                 │          │        │")
    print("   ├─────────────────────────┼──────────┼────────┤")
    print("   │ PYTHIA/SOL              │$2.93M    │23.5%   │")
    print("   │ raydium                 │          │        │")
    print("   └─────────────────────────┴──────────┴────────┘")

if __name__ == "__main__":
    print("📏 PYTHIA表格宽度修复测试工具")
    print("=" * 60)
    
    # 显示宽度修复总结
    show_width_fix_summary()
    
    # 测试表格宽度修复
    success = test_table_width_fix()
    
    # 显示期望布局
    test_expected_layout()
    
    if success:
        print(f"\n🎉 表格宽度修复测试完成!")
        print(f"💡 请在浏览器中查看生成的测试报告")
        print(f"🔍 验证表格是否刚好适合宽度，流动性列是否左移")
    else:
        print(f"\n❌ 修复未完全成功，需要进一步调整")

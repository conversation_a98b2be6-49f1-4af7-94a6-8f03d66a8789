#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PYTHIA项目统一配置文件
包含Telegram Bot配置和代币分析配置
"""

# ================================
# Telegram Bot 配置
# ================================

# Telegram Bot Token
TELEGRAM_BOT_TOKEN = "**********************************************"

# Bot 用户名
BOT_USERNAME = "@pythia_is_ai_bot"

# Telegram群组/频道配置（用于发送报告）
TELEGRAM_REPORT_CHATS = [
    "@RjIOrkEpYxpkOGEy",   # PYTHIA群组
    # 可以添加更多群组/频道/用户ID
    # "-1001234567890",     # 群组ID（负数）
    # "123456789",          # 用户ID（正数）
]

# 报告发送配置
REPORT_SEND_CONFIG = {
    "auto_send_enabled": True,   # 是否自动发送报告
    "send_on_generation": True,  # 生成报告时自动发送
    "send_to_groups": True,      # 发送到配置的群组
    "send_to_subscribers": True, # 发送给订阅用户
    "include_summary": True,     # 是否包含摘要文字
}

# 对话记忆上限
MEMORY_LIMIT = 5

# AI API 配置 - 使用Google Gemini API
GEMINI_API_KEY = "AIzaSyBSllSwrObqvUiXqFG5RUJXB6woZoBSaTk"
GEMINI_MODEL = "gemini-2.5-flash"  # 使用Gemini 2.5 Flash模型
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models"

# 记忆文件路径
MEMORY_FILE = "memory.json"

# Bot 响应配置
ERROR_RESPONSE = "吱...我的头部按摩器（电极）好像有点短路了，让我先去找实验室的服务员修一下。你们稍等，我很快回来！🐭"

# 日志配置
LOG_LEVEL = "INFO"

# ================================
# PYTHIA代币分析配置
# ================================

# PYTHIA代币基本信息
PYTHIA_TOKEN_INFO = {
    "name": "PYTHIA",
    "symbol": "PYTHIA",
    "chain": "solana",
    "description": "世界首个生物-AI融合项目代币",

    # PYTHIA代币实际地址
    "contract_address": "CreiuhfwdWCN5mJbMJtA9bBpYQrQF2tCBuZwSPWfpump",

    # 已知的交易对地址（如果有的话）
    "known_pairs": [
        # "pair_address_1",
        # "pair_address_2"
    ]
}

# API配置
API_CONFIG = {
    "base_url": "https://api.dexscreener.com",
    "timeout": 30,  # 增加超时时间到30秒
    "retry_attempts": 5,  # 增加重试次数到5次
    "retry_delay": 3,  # 增加重试间隔到3秒

    # 速率限制（每分钟）
    "rate_limits": {
        "token_profiles": 60,
        "token_boosts": 60,
        "pairs": 300,
        "search": 300
    },
    
    # 网络优化配置
    "connection_pool_size": 10,
    "max_retries_per_connection": 3,
    "backoff_factor": 0.3,  # 指数退避因子
    "status_forcelist": [500, 502, 503, 504, 429],  # 需要重试的HTTP状态码
}

# 分析配置
ANALYSIS_CONFIG = {
    "price_alert_thresholds": {
        "high_change": 20.0,  # 24h价格变化超过20%
        "volume_spike": 1000000,  # 24h交易量超过100万美元
        "liquidity_low": 50000,   # 流动性低于5万美元
    },

    "data_filter_settings": {
        # 适合PYTHIA实际数据的过滤标准 - 平衡质量和可用性
        "min_market_cap": 1000,       # 最小市值1千美元 - 宽松标准
        "min_liquidity": 500,         # 最小流动性500美元 - 宽松标准
        "min_volume_24h": 100,        # 最小24小时交易量100美元 - 宽松标准
        "min_price": 0.00001,         # 最小价格0.00001美元 - 支持低价代币
        "max_price": 1000000,         # 最大价格100万美元 - 宽松上限

        # 高级过滤条件 - 调整为宽松模式
        "min_transaction_count": 5,   # 最小24小时交易笔数 - 降低到5笔
        "max_price_change": 2000,     # 最大24小时价格变化2000% - 允许高波动
        "min_age_hours": 1,           # 最小交易对存在时间1小时 - 允许新交易对
        "require_both_tokens": False, # 不强制要求完整代币信息

        # 质量评分系统 - 降低门槛
        "enable_quality_scoring": True,  # 启用质量评分
        "min_quality_score": 20,         # 最小质量评分20分 - 大幅降低门槛

        # DEX白名单 - 暂时禁用以包含更多交易对
        "trusted_dexes": [
            "uniswap", "pancakeswap", "sushiswap", "quickswap",
            "traderjoe", "spookyswap", "spiritswap", "apeswap",
            "raydium", "orca", "serum", "dextools", "dexscreener"
        ],
        "enable_dex_whitelist": False,  # 禁用DEX白名单，允许所有DEX
    },

    "report_settings": {
        "save_to_file": True,
        "include_charts": False,
        "detailed_analysis": True,
        "show_filtered_stats": True,  # 是否在报告中显示过滤统计信息
    },

    # 定时运行配置
    "auto_run_settings": {
        "enabled": True,  # 是否启用自动运行
        "interval_minutes": 5,  # 生成报告的间隔时间（分钟）- 每小时一次
        "max_reports_per_day": 721,  # 每天最大报告数量（每小时一次=24次/天）
        "run_on_startup": True,  # 启动时立即生成一次报告
        "quiet_mode": False,  # 静默模式（减少输出信息）
    }

    # 常用间隔时间配置参考：
    # interval_minutes = 5   -> 每5分钟一次  (288次/天)
    # interval_minutes = 10  -> 每10分钟一次 (144次/天)
    # interval_minutes = 15  -> 每15分钟一次 (96次/天)
    # interval_minutes = 30  -> 每30分钟一次 (48次/天)
    # interval_minutes = 60  -> 每小时一次   (24次/天)
}

# 搜索关键词
SEARCH_KEYWORDS = [
    "PYTHIA",
    "pythia",
    # 可以添加其他相关搜索词
]

# 输出格式配置
OUTPUT_CONFIG = {
    "currency_format": "${:,.2f}",
    "percentage_format": "{:.2f}%",
    "large_number_format": "{:,.0f}",
    "datetime_format": "%Y-%m-%d %H:%M:%S",
    "data_directory": "data",
    
    # Telegram配置
    "telegram": {
        "bot_token": "**********************************************",
        "chat_id": "@RjIOrkEpYxpkOGEy"  # 使用群组用户名
    }
}

# ================================
# 数据过滤配置说明
# ================================

# 常用市值过滤配置参考：
# min_market_cap = 10000   -> 过滤市值小于1万美元的交易对
# min_market_cap = 50000   -> 过滤市值小于5万美元的交易对（推荐）
# min_market_cap = 100000  -> 过滤市值小于10万美元的交易对
# min_market_cap = 500000  -> 过滤市值小于50万美元的交易对（严格模式）
# min_market_cap = 1000000 -> 过滤市值小于100万美元的交易对（极严格模式）

# 常用流动性过滤配置参考：
# min_liquidity = 5000     -> 过滤流动性小于5千美元的交易对
# min_liquidity = 10000    -> 过滤流动性小于1万美元的交易对（推荐）
# min_liquidity = 50000    -> 过滤流动性小于5万美元的交易对
# min_liquidity = 100000   -> 过滤流动性小于10万美元的交易对（严格模式）

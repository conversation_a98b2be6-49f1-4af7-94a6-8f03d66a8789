#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高质量交易对过滤功能
"""

import sys
import os
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_quality_filter():
    """测试高质量交易对过滤功能"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print("🔍 测试高质量交易对过滤功能")
        print("=" * 60)
        
        # 创建分析器实例
        analyzer = PythiaIntegratedAnalyzer()
        
        # 创建测试数据 - 包含高质量和低质量交易对
        test_pairs = [
            # 高质量交易对1
            {
                'pairAddress': 'high_quality_1',
                'marketCap': 5000000,      # 500万市值
                'liquidity': {'usd': 200000},  # 20万流动性
                'volume': {'h24': 100000},     # 10万交易量
                'priceUsd': '1.5',
                'priceChange': {'h24': 5.2},   # 5.2%变化
                'txns': {'h24': {'buys': 150, 'sells': 120}},  # 270笔交易
                'dexId': 'uniswap',
                'baseToken': {'symbol': 'PYTHIA'},
                'quoteToken': {'symbol': 'USDT'},
                'pairCreatedAt': time.time() - 86400 * 7  # 7天前创建
            },
            # 高质量交易对2
            {
                'pairAddress': 'high_quality_2',
                'marketCap': 2000000,      # 200万市值
                'liquidity': {'usd': 150000},  # 15万流动性
                'volume': {'h24': 80000},      # 8万交易量
                'priceUsd': '0.8',
                'priceChange': {'h24': -3.1},  # -3.1%变化
                'txns': {'h24': {'buys': 100, 'sells': 90}},  # 190笔交易
                'dexId': 'pancakeswap',
                'baseToken': {'symbol': 'PYTHIA'},
                'quoteToken': {'symbol': 'BNB'},
                'pairCreatedAt': time.time() - 86400 * 3  # 3天前创建
            },
            # 低质量交易对1 - 市值太低
            {
                'pairAddress': 'low_quality_1',
                'marketCap': 50000,        # 5万市值 (低于10万阈值)
                'liquidity': {'usd': 20000},   # 2万流动性
                'volume': {'h24': 5000},       # 5千交易量
                'priceUsd': '0.1',
                'priceChange': {'h24': 15.0},
                'txns': {'h24': {'buys': 20, 'sells': 15}},  # 35笔交易
                'dexId': 'unknown_dex',
                'baseToken': {'symbol': 'SCAM'},
                'quoteToken': {'symbol': 'USDT'},
                'pairCreatedAt': time.time() - 3600  # 1小时前创建
            },
            # 低质量交易对2 - 流动性太低
            {
                'pairAddress': 'low_quality_2',
                'marketCap': 500000,       # 50万市值
                'liquidity': {'usd': 10000},   # 1万流动性 (低于5万阈值)
                'volume': {'h24': 2000},       # 2千交易量
                'priceUsd': '2.0',
                'priceChange': {'h24': 800.0}, # 800%变化 (异常波动)
                'txns': {'h24': {'buys': 10, 'sells': 5}},   # 15笔交易
                'dexId': 'sushiswap',
                'baseToken': {'symbol': 'VOLATILE'},
                'quoteToken': {'symbol': 'ETH'},
                'pairCreatedAt': time.time() - 86400 * 30  # 30天前创建
            },
            # 中等质量交易对
            {
                'pairAddress': 'medium_quality_1',
                'marketCap': 800000,       # 80万市值
                'liquidity': {'usd': 80000},   # 8万流动性
                'volume': {'h24': 25000},      # 2.5万交易量
                'priceUsd': '0.5',
                'priceChange': {'h24': 12.0},  # 12%变化
                'txns': {'h24': {'buys': 60, 'sells': 55}}, # 115笔交易
                'dexId': 'quickswap',
                'baseToken': {'symbol': 'MEDIUM'},
                'quoteToken': {'symbol': 'MATIC'},
                'pairCreatedAt': time.time() - 86400 * 5  # 5天前创建
            }
        ]
        
        print(f"📊 测试数据:")
        print(f"   总交易对数: {len(test_pairs)}")
        print(f"   高质量交易对: 2个")
        print(f"   低质量交易对: 2个") 
        print(f"   中等质量交易对: 1个")
        
        # 执行过滤
        print(f"\n🔍 执行高质量过滤...")
        start_time = time.time()
        
        filtered_pairs, filter_stats = analyzer.filter_pairs_by_criteria(test_pairs)
        
        filter_time = time.time() - start_time
        
        print(f"\n📈 过滤结果:")
        print(f"   过滤耗时: {filter_time:.3f}秒")
        print(f"   原始交易对: {filter_stats['total_pairs']}")
        print(f"   过滤后交易对: {filter_stats['final_pairs']}")
        print(f"   过滤率: {((filter_stats['total_pairs'] - filter_stats['final_pairs']) / filter_stats['total_pairs'] * 100):.1f}%")
        
        # 显示通过过滤的交易对
        print(f"\n✅ 通过过滤的高质量交易对:")
        for i, pair in enumerate(filtered_pairs, 1):
            quality_score = analyzer.calculate_pair_quality_score(pair)
            print(f"   {i}. {pair['pairAddress']}")
            print(f"      市值: ${pair['marketCap']:,}")
            print(f"      流动性: ${pair['liquidity']['usd']:,}")
            print(f"      交易量: ${pair['volume']['h24']:,}")
            print(f"      质量评分: {quality_score:.1f}/100")
            print(f"      DEX: {pair['dexId']}")
        
        # 性能分析
        print(f"\n🚀 性能分析:")
        if filter_stats['final_pairs'] < filter_stats['total_pairs']:
            reduction = (filter_stats['total_pairs'] - filter_stats['final_pairs']) / filter_stats['total_pairs'] * 100
            print(f"   数据处理量减少: {reduction:.0f}%")
            print(f"   预期性能提升: {reduction * 0.8:.0f}%")  # 估算性能提升
        
        # 验证过滤效果
        print(f"\n🎯 过滤效果验证:")
        expected_high_quality = 2  # 预期通过的高质量交易对数量
        if filter_stats['final_pairs'] == expected_high_quality:
            print(f"   ✅ 过滤效果完美! 正确识别了{expected_high_quality}个高质量交易对")
        elif filter_stats['final_pairs'] > expected_high_quality:
            print(f"   ⚠️ 过滤可能不够严格，通过了{filter_stats['final_pairs']}个交易对")
        else:
            print(f"   ⚠️ 过滤可能过于严格，只通过了{filter_stats['final_pairs']}个交易对")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quality_scoring():
    """测试质量评分系统"""
    try:
        from pythia_integrated_complete import PythiaIntegratedAnalyzer
        
        print("\n🏆 测试质量评分系统")
        print("=" * 40)
        
        analyzer = PythiaIntegratedAnalyzer()
        
        # 测试不同质量的交易对
        test_cases = [
            {
                'name': '顶级交易对',
                'pair': {
                    'marketCap': 50000000,     # 5000万
                    'liquidity': {'usd': 2000000},  # 200万
                    'volume': {'h24': 5000000},     # 500万
                    'priceChange': {'h24': 2.5},    # 2.5%
                    'txns': {'h24': {'buys': 2000, 'sells': 1800}},
                    'dexId': 'uniswap'
                }
            },
            {
                'name': '优质交易对',
                'pair': {
                    'marketCap': 5000000,      # 500万
                    'liquidity': {'usd': 500000},   # 50万
                    'volume': {'h24': 1000000},     # 100万
                    'priceChange': {'h24': 8.0},    # 8%
                    'txns': {'h24': {'buys': 500, 'sells': 450}},
                    'dexId': 'pancakeswap'
                }
            },
            {
                'name': '一般交易对',
                'pair': {
                    'marketCap': 500000,       # 50万
                    'liquidity': {'usd': 100000},   # 10万
                    'volume': {'h24': 50000},       # 5万
                    'priceChange': {'h24': 25.0},   # 25%
                    'txns': {'h24': {'buys': 100, 'sells': 120}},
                    'dexId': 'quickswap'
                }
            },
            {
                'name': '低质交易对',
                'pair': {
                    'marketCap': 50000,        # 5万
                    'liquidity': {'usd': 10000},    # 1万
                    'volume': {'h24': 1000},        # 1千
                    'priceChange': {'h24': 150.0},  # 150%
                    'txns': {'h24': {'buys': 10, 'sells': 25}},
                    'dexId': 'unknown'
                }
            }
        ]
        
        for test_case in test_cases:
            score = analyzer.calculate_pair_quality_score(test_case['pair'])
            print(f"   {test_case['name']}: {score:.1f}/100分")
            
        print(f"\n✅ 质量评分系统测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 质量评分测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_quality_filter()
    success2 = test_quality_scoring()
    
    if success1 and success2:
        print(f"\n🎉 所有测试通过! 高质量交易对过滤功能正常工作")
    else:
        print(f"\n❌ 部分测试失败，请检查配置")

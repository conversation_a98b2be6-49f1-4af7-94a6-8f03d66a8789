#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高质量交易对过滤配置管理器
提供不同的过滤模式和配置选项
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 预定义的过滤模式
FILTER_MODES = {
    "strict": {
        "name": "严格模式",
        "description": "最高质量标准，只保留顶级交易对",
        "settings": {
            "min_market_cap": 1000000,      # 100万美元
            "min_liquidity": 200000,        # 20万美元
            "min_volume_24h": 100000,       # 10万美元
            "min_price": 0.01,              # 0.01美元
            "max_price": 50000,             # 5万美元
            "min_transaction_count": 200,   # 200笔交易
            "max_price_change": 100,        # 100%最大变化
            "min_age_hours": 168,           # 7天最小年龄
            "min_quality_score": 80,        # 80分最小质量
            "enable_dex_whitelist": True,
            "trusted_dexes": ["uniswap", "pancakeswap", "sushiswap"]
        }
    },
    
    "balanced": {
        "name": "平衡模式",
        "description": "平衡质量和数量，适合日常使用",
        "settings": {
            "min_market_cap": 100000,       # 10万美元
            "min_liquidity": 50000,         # 5万美元
            "min_volume_24h": 10000,        # 1万美元
            "min_price": 0.001,             # 0.001美元
            "max_price": 100000,            # 10万美元
            "min_transaction_count": 50,    # 50笔交易
            "max_price_change": 500,        # 500%最大变化
            "min_age_hours": 24,            # 1天最小年龄
            "min_quality_score": 60,        # 60分最小质量
            "enable_dex_whitelist": True,
            "trusted_dexes": [
                "uniswap", "pancakeswap", "sushiswap", "quickswap", 
                "traderjoe", "spookyswap", "raydium", "orca"
            ]
        }
    },
    
    "relaxed": {
        "name": "宽松模式",
        "description": "较低门槛，包含更多交易对",
        "settings": {
            "min_market_cap": 10000,        # 1万美元
            "min_liquidity": 5000,          # 5千美元
            "min_volume_24h": 1000,         # 1千美元
            "min_price": 0.0001,            # 0.0001美元
            "max_price": 1000000,           # 100万美元
            "min_transaction_count": 10,    # 10笔交易
            "max_price_change": 1000,       # 1000%最大变化
            "min_age_hours": 1,             # 1小时最小年龄
            "min_quality_score": 30,        # 30分最小质量
            "enable_dex_whitelist": False,  # 不启用DEX白名单
            "trusted_dexes": []
        }
    },
    
    "performance": {
        "name": "性能模式",
        "description": "优先考虑性能，大幅减少数据量",
        "settings": {
            "min_market_cap": 500000,       # 50万美元
            "min_liquidity": 100000,        # 10万美元
            "min_volume_24h": 50000,        # 5万美元
            "min_price": 0.01,              # 0.01美元
            "max_price": 10000,             # 1万美元
            "min_transaction_count": 100,   # 100笔交易
            "max_price_change": 200,        # 200%最大变化
            "min_age_hours": 72,            # 3天最小年龄
            "min_quality_score": 70,        # 70分最小质量
            "enable_dex_whitelist": True,
            "trusted_dexes": ["uniswap", "pancakeswap"]  # 只保留顶级DEX
        }
    }
}

def display_filter_modes():
    """显示所有可用的过滤模式"""
    print("🔍 可用的过滤模式:")
    print("=" * 50)
    
    for mode_key, mode_info in FILTER_MODES.items():
        print(f"\n📋 {mode_key.upper()} - {mode_info['name']}")
        print(f"   描述: {mode_info['description']}")
        
        settings = mode_info['settings']
        print(f"   配置:")
        print(f"      最小市值: ${settings['min_market_cap']:,}")
        print(f"      最小流动性: ${settings['min_liquidity']:,}")
        print(f"      最小交易量: ${settings['min_volume_24h']:,}")
        print(f"      最小质量评分: {settings['min_quality_score']}")
        print(f"      DEX白名单: {'启用' if settings['enable_dex_whitelist'] else '禁用'}")

def apply_filter_mode(mode_name):
    """应用指定的过滤模式到配置文件"""
    if mode_name not in FILTER_MODES:
        print(f"❌ 未知的过滤模式: {mode_name}")
        print(f"可用模式: {', '.join(FILTER_MODES.keys())}")
        return False
    
    try:
        # 读取当前配置
        import config
        
        # 更新过滤设置
        mode_settings = FILTER_MODES[mode_name]['settings']
        
        # 创建新的配置内容
        new_filter_settings = {
            **mode_settings,
            "require_both_tokens": True,
            "enable_quality_scoring": True,
        }
        
        print(f"🔧 应用 {FILTER_MODES[mode_name]['name']} 配置...")
        
        # 这里我们创建一个临时配置文件来演示
        # 实际使用时可以直接修改config.py
        temp_config = {
            "data_filter_settings": new_filter_settings
        }
        
        # 保存临时配置
        with open(f"filter_config_{mode_name}.json", 'w', encoding='utf-8') as f:
            json.dump(temp_config, f, indent=4, ensure_ascii=False)
        
        print(f"✅ {FILTER_MODES[mode_name]['name']} 配置已保存到 filter_config_{mode_name}.json")
        print(f"📋 配置详情:")
        
        for key, value in new_filter_settings.items():
            if isinstance(value, list):
                print(f"   {key}: {', '.join(value) if value else '无'}")
            else:
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用配置失败: {e}")
        return False

def estimate_performance_impact(mode_name):
    """估算过滤模式对性能的影响"""
    if mode_name not in FILTER_MODES:
        return
    
    mode_info = FILTER_MODES[mode_name]
    settings = mode_info['settings']
    
    print(f"\n📊 {mode_info['name']} 性能影响估算:")
    print("-" * 40)
    
    # 基于过滤条件估算数据减少量
    strictness_score = 0
    
    # 市值门槛影响
    if settings['min_market_cap'] >= 1000000:
        strictness_score += 40
    elif settings['min_market_cap'] >= 100000:
        strictness_score += 25
    elif settings['min_market_cap'] >= 10000:
        strictness_score += 10
    
    # 流动性门槛影响
    if settings['min_liquidity'] >= 200000:
        strictness_score += 30
    elif settings['min_liquidity'] >= 50000:
        strictness_score += 20
    elif settings['min_liquidity'] >= 5000:
        strictness_score += 10
    
    # 质量评分影响
    if settings['min_quality_score'] >= 80:
        strictness_score += 20
    elif settings['min_quality_score'] >= 60:
        strictness_score += 15
    elif settings['min_quality_score'] >= 30:
        strictness_score += 5
    
    # DEX白名单影响
    if settings['enable_dex_whitelist']:
        strictness_score += 10
    
    # 估算数据减少量
    data_reduction = min(strictness_score, 95)  # 最多减少95%
    
    print(f"   预期数据减少量: {data_reduction}%")
    print(f"   预期性能提升: {data_reduction * 0.8:.0f}%")
    
    if data_reduction >= 80:
        performance_level = "🚀 极高"
    elif data_reduction >= 60:
        performance_level = "⚡ 高"
    elif data_reduction >= 40:
        performance_level = "📈 中等"
    else:
        performance_level = "🐌 较低"
    
    print(f"   性能提升等级: {performance_level}")
    
    # 给出建议
    if data_reduction >= 80:
        print(f"   💡 建议: 适合频繁运行和快速响应场景")
    elif data_reduction >= 60:
        print(f"   💡 建议: 适合日常使用，平衡质量和性能")
    elif data_reduction >= 40:
        print(f"   💡 建议: 适合需要更多数据的分析场景")
    else:
        print(f"   💡 建议: 适合全面分析，但性能较慢")

def interactive_config():
    """交互式配置过滤参数"""
    print("\n🛠️ 交互式过滤配置")
    print("=" * 30)
    
    try:
        # 选择基础模式
        print("请选择基础模式:")
        for i, (mode_key, mode_info) in enumerate(FILTER_MODES.items(), 1):
            print(f"   {i}. {mode_key} - {mode_info['name']}")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        mode_keys = list(FILTER_MODES.keys())
        if choice.isdigit() and 1 <= int(choice) <= len(mode_keys):
            selected_mode = mode_keys[int(choice) - 1]
            print(f"\n✅ 已选择: {FILTER_MODES[selected_mode]['name']}")
            
            # 显示性能影响
            estimate_performance_impact(selected_mode)
            
            # 询问是否应用
            apply = input(f"\n是否应用此配置? (y/n): ").strip().lower()
            if apply in ['y', 'yes', '是']:
                return apply_filter_mode(selected_mode)
            else:
                print("❌ 已取消配置应用")
                return False
        else:
            print("❌ 无效选择")
            return False
            
    except KeyboardInterrupt:
        print("\n❌ 用户取消")
        return False
    except Exception as e:
        print(f"❌ 配置失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 PYTHIA 高质量交易对过滤配置管理器")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode in FILTER_MODES:
            apply_filter_mode(mode)
            estimate_performance_impact(mode)
        elif mode == "list":
            display_filter_modes()
        elif mode == "interactive":
            interactive_config()
        else:
            print(f"❌ 未知命令: {mode}")
            print("可用命令: list, interactive, strict, balanced, relaxed, performance")
    else:
        # 默认显示所有模式
        display_filter_modes()
        print(f"\n💡 使用方法:")
        print(f"   python filter_config_manager.py list          # 显示所有模式")
        print(f"   python filter_config_manager.py interactive   # 交互式配置")
        print(f"   python filter_config_manager.py strict        # 应用严格模式")
        print(f"   python filter_config_manager.py balanced      # 应用平衡模式")
        print(f"   python filter_config_manager.py relaxed       # 应用宽松模式")
        print(f"   python filter_config_manager.py performance   # 应用性能模式")

if __name__ == "__main__":
    main()
